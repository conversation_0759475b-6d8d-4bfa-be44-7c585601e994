import React, { useState, useEffect } from "react";
import { flushSync } from "react-dom";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { FiUpload, FiInfo, FiCheck } from "react-icons/fi";
import { FaTimes, FaCheckCircle, FaCloudUploadAlt, FaSpinner } from "react-icons/fa";
import DynamicHeading from "../../components/common/DynamicHeading";
import SportsCard from "../../components/common/SportsCard";
import SellerLayout from "../../components/seller/SellerLayout";
import SummernoteEditor from "../../components/common/SummernoteEditor";
import UploadProgressBar from "../../components/common/UploadProgressBar";
import ContentSubmissionModal from "../../components/seller/ContentSubmissionModal";

import PreviewStatusIndicator from "../../components/common/PreviewStatusIndicator";

import { createContent, uploadContentFile } from "../../redux/slices/contentSlice";
import chunkedUploadService from "../../services/chunkedUploadService";
import { API_BASE_URL } from "../../utils/constants";
import { validateFileByContentType, getAcceptAttribute, isFileUploadDisabled, FILE_SIZE_LIMITS } from "../../utils/fileValidation";
import { getImageUrl, getPlaceholderImage } from "../../utils/constants";
import {
  VALIDATION_LIMITS,
} from "../../utils/textValidation";
import { toUTC } from "../../utils/timezoneUtils";
import { getPlatformCommission } from "../../services/settingsService";
import { showError, showSuccess } from "../../utils/toast";
import "../../styles/AddStrategy.css";
import TimezoneInfo from "../../components/common/TimezoneInfo";
import TimezoneErrorBoundary from "../../components/common/TimezoneErrorBoundary";

// Helper function to format file size
const formatFileSize = (bytes) => {
  if (bytes >= 1024 * 1024 * 1024) {
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)}GB`;
  } else if (bytes >= 1024 * 1024) {
    return `${(bytes / (1024 * 1024)).toFixed(0)}MB`;
  } else if (bytes >= 1024) {
    return `${(bytes / 1024).toFixed(0)}KB`;
  }
  return `${bytes}B`;
};

const AddStrategy = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { isLoading, isError, error } = useSelector((state) => state.content);
  const { user } = useSelector((state) => state.auth);

  // Form state - Visible fields as per screenshot
  const [formData, setFormData] = useState({
    // Visible fields
    title: "",
    category: "",
    coachName: "", // New field for Coach/Seller/Academy Name
    description: "",
    fileUrl: "",
    aboutCoach: "",
    strategicContent: "",

    // Fields from JSON example
    sport: "Basketball", // Default value
    contentType: "", // Empty for validation
    thumbnailUrl: "", // For thumbnail upload
    tags: [], // For tags like basketball, technique, training
    difficulty: "", // Empty for validation
    saleType: "", // Empty for validation
    price: "", // Empty for validation
    allowCustomRequests: false, // Default value - set to false as custom requests are disabled

    // Auction-specific fields
    auctionDetails: {
      basePrice: "",
      auctionStartDate: "",
      auctionEndDate: "",
      minimumBidIncrement: "",
      allowOfferBeforeAuctionStart: false,
    },

    // Other hidden fields with default values for backend compatibility
    previewUrl: "", // Hidden - not shown in screenshot
    duration: "", // Hidden - not shown in screenshot
    videoLength: "", // Hidden - not shown in screenshot
    fileSize: "", // Hidden - not shown in screenshot
    prerequisites: [], // Hidden - not shown in screenshot
    learningObjectives: [], // Hidden - not shown in screenshot
    equipment: [], // Hidden - not shown in screenshot
    customRequestPrice: "", // Hidden - not shown in screenshot
    status: "Draft", // Default value - hidden to match UI
    visibility: "Public", // Default value - hidden to match UI
  });

  // Array field states
  const [newTag, setNewTag] = useState("");

  // File upload state
  const [uploadedFile, setUploadedFile] = useState(null);

  // Upload progress states
  const [showUploadProgress, setShowUploadProgress] = useState(false);
  const [currentUploadType, setCurrentUploadType] = useState("");
  const [currentFileName, setCurrentFileName] = useState("");
  const [createdContentId, setCreatedContentId] = useState(null);
  const [showPreviewStatus, setShowPreviewStatus] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Enhanced upload states
  const [uploadStats, setUploadStats] = useState({});
  const [uploadError, setUploadError] = useState(null);
  const [isRetrying, setIsRetrying] = useState(false);
  const [canRetry, setCanRetry] = useState(false);
  const [currentUploadId, setCurrentUploadId] = useState(null);
  const [useChunkedUpload, setUseChunkedUpload] = useState(false);

  // Thumbnail validation states
  const [thumbnailError, setThumbnailError] = useState("");
  const [showThumbnailPreview, setShowThumbnailPreview] = useState(true);

  // Validation state
  const [validationErrors, setValidationErrors] = useState({});
  const [showValidation, setShowValidation] = useState(false);
  const [formSubmitted, setFormSubmitted] = useState(false);

  // Platform commission state
  const [platformCommission, setPlatformCommission] = useState(0);

  // Content submission modal state
  const [showSubmissionModal, setShowSubmissionModal] = useState(false);
  const [submittedContentTitle, setSubmittedContentTitle] = useState("");

  // Load sports data and check payment setup on component mount
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        // Fetch platform commission
        const commissionResponse = await getPlatformCommission();
        if (commissionResponse.data.success) {
          setPlatformCommission(commissionResponse.data.data.platformCommission);
        }
      } catch (error) {
        console.error("Error fetching initial data:", error);
        showError("Failed to load required data");
      }
    };

    fetchInitialData();
  }, []);

  // Check Stripe Connect status on component mount
  useEffect(() => {
    const checkPaymentSetup = () => {
      const hasStripeConnect = user?.paymentInfo?.stripeConnectId;

      if (!hasStripeConnect) {
        showError("Complete your payout setup to create content.", {
          autoClose: false,
          closeOnClick: false,
          closeButton: true,
        });

        // Show confirmation modal for redirection
        const shouldRedirect = window.confirm(
          "⚠️ Payment Setup Required\n\n" +
          "You need to complete your payment setup before creating content. " +
          "This ensures you can receive payments from your sales.\n\n" +
          "Would you like to go to Payment Settings now?"
        );

        if (shouldRedirect) {
          navigate("/seller/payment-settings");
        } else {
          navigate("/seller/my-sports-strategies");
        }

        return false;
      }

      return true;
    };

    // Only check if user data is loaded
    if (user) {
      checkPaymentSetup();
    }
  }, [user, navigate]);

  // If no Stripe Connect, show blocking message
  if (user && !user?.paymentInfo?.stripeConnectId) {
    return (
      <SellerLayout>
        <div className="payment-setup-required">
          <div className="payment-setup-card">
            <div className="payment-setup-icon">🏦</div>
            <h2>Payment Setup Required</h2>
            <p>
              You need to complete your payment setup before creating content.
              This ensures you can receive payments from your content sales.
            </p>
            <div className="payment-setup-benefits">
              <div className="benefit-item">
                <span className="benefit-icon">✅</span>
                <span>Secure payments powered by Stripe</span>
              </div>
              <div className="benefit-item">
                <span className="benefit-icon">✅</span>
                <span>Automatic payouts to your bank account</span>
              </div>
              <div className="benefit-item">
                <span className="benefit-icon">✅</span>
                <span>You keep {100 - platformCommission}% of each sale</span>
              </div>
            </div>
            <div className="payment-setup-actions">
              <button
                className="btn btn-primary"
                onClick={() => navigate("/seller/payment-settings")}
              >
                Complete Payment Setup
              </button>
              <button
                className="btn btn-outline"
                onClick={() => navigate("/seller/my-sports-strategies")}
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
      </SellerLayout>
    );
  }

  // Handle input changes with validation
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === "checkbox" ? checked : value;

    if (name === "auctionDetails.auctionStartDate" || name === "auctionDetails.auctionEndDate") {
      // Convert the datetime-local value to UTC
      setFormData((prev) => ({
        ...prev,
        auctionDetails: {
          ...prev.auctionDetails,
          [name.split('.')[1]]: value ? toUTC(new Date(value)).toISOString().slice(0, 16) : "",
        }
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: newValue,
      }));

      // Special handling for content type changes
      if (name === "contentType" && uploadedFile) {
        // Validate existing file against new content type
        const validation = validateFileByContentType(uploadedFile, newValue);
        if (!validation.isValid) {
          // Clear the file if it doesn't match the new content type
          setUploadedFile(null);
          setFormData(prev => ({
            ...prev,
            fileUrl: "",
            fileSize: "",
          }));
          showError(`Current file is not valid for ${newValue} content type. Please upload a new file.`);

          // Clear file input if it exists
          const fileInput = document.querySelector('input[type="file"]');
          if (fileInput) {
            fileInput.value = "";
          }
        }
      }
    }

    // Validate the field if it's a text field
    if (type !== "checkbox") {
      validateField(name, newValue);
    }
  };

  // Handle field blur (when user clicks away from field)
  const handleFieldBlur = (e) => {
    const { name, value } = e.target;
    validateField(name, value);
  };

  // Handle Summernote changes with validation
  const handleSummernoteChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Validate word count for rich text fields
    validateField(field, value);
  };

  // Validate individual field
  const validateField = (fieldName, value) => {
    const errors = { ...validationErrors };

    switch (fieldName) {
      case "title": {
        if (!value.trim()) {
          errors.title = "Strategy title is required";
        } else if (value.length > VALIDATION_LIMITS.TITLE.maxChars) {
          errors.title = `Title cannot exceed ${VALIDATION_LIMITS.TITLE.maxChars} characters`;
        } else {
          delete errors.title;
        }
        break;
      }

      case "category": {
        if (!value) {
          errors.category = "Please select a category";
        } else {
          delete errors.category;
        }
        break;
      }

      case "coachName": {
        if (!value.trim()) {
          errors.coachName = "Coach/Seller/Academy name is required";
        } else if (value.length > VALIDATION_LIMITS.COACH_NAME.maxChars) {
          errors.coachName = `Coach name cannot exceed ${VALIDATION_LIMITS.COACH_NAME.maxChars} characters`;
        } else {
          delete errors.coachName;
        }
        break;
      }

      case "description": {
        const cleanDescription = value.replace(/<[^>]*>/g, "").trim();
        if (!cleanDescription) {
          errors.description = 'Strategy description is required';
        } else {
          delete errors.description;
        }
        break;
      }

      case "aboutCoach": {
        const cleanAboutCoach = value.replace(/<[^>]*>/g, "").trim();
        if (!cleanAboutCoach) {
          errors.aboutCoach = 'About the coach information is required';
        } else {
          delete errors.aboutCoach;
        }
        break;
      }

      case "strategicContent": {
        const cleanStrategicContent = value.replace(/<[^>]*>/g, "").trim();
        if (!cleanStrategicContent) {
          errors.strategicContent = 'Strategic content description is required';
        } else {
          delete errors.strategicContent;
        }
        break;
      }

      case "contentType": {
        if (!value) {
          errors.contentType = "Please select a content type";
        } else {
          delete errors.contentType;
        }
        break;
      }

      case "difficulty": {
        if (!value) {
          errors.difficulty = "Please select a difficulty level";
        } else {
          delete errors.difficulty;
        }
        break;
      }

      case "saleType": {
        if (!value) {
          errors.saleType = "Please select a sale type";
        } else {
          delete errors.saleType;
        }
        break;
      }

      case "price": {
        if (!value || value <= 0) {
          errors.price = "Please enter a valid price greater than $0";
        } else {
          delete errors.price;
        }
        break;
      }

      default:
        break;
    }

    setValidationErrors(errors);
  };

  // Handle file upload
  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      // Ensure content type is selected first
      if (!formData.contentType) {
        showError("Please select a content type before uploading a file");
        e.target.value = ""; // Clear the file input
        return;
      }

      // Validate file based on selected content type
      const validation = validateFileByContentType(file, formData.contentType);
      if (!validation.isValid) {
        showError(validation.message);
        e.target.value = ""; // Clear the file input
        return;
      }

      setUploadedFile(file);
      setShowUploadProgress(true);
      setCurrentUploadType("content file");
      setCurrentFileName(file.name);
      setUploadProgress(0);
      setUploadError(null);
      setCanRetry(false);
      setUploadStats({});

      // Determine if we should use chunked upload (for files > 100MB)
      const shouldUseChunkedUpload = file.size > 100 * 1024 * 1024; // 100MB threshold
      setUseChunkedUpload(shouldUseChunkedUpload);

      try {
        let result;

        if (shouldUseChunkedUpload) {
          console.log(`[Upload] Using chunked upload for large file: ${file.name} (${Math.round(file.size / (1024 * 1024))}MB)`);

          result = await chunkedUploadService.uploadFile(
            file,
            formData.contentType, // Pass content type
            (stats) => {
              setUploadProgress(stats.progress);
              setUploadStats(stats);
            },
            (error) => {
              setUploadError(error);
              setCanRetry(true);
            },
            (chunkIndex, retryCount, maxRetries) => {
              setIsRetrying(true);
              console.log(`[Upload] Retrying chunk ${chunkIndex} (${retryCount}/${maxRetries})`);
            }
          );
        } else {
          console.log(`[Upload] Using standard upload for file: ${file.name} (${Math.round(file.size / (1024 * 1024))}MB)`);

          const formDataUpload = new FormData();
          formDataUpload.append("file", file);
          formDataUpload.append("type", "content");

          result = await dispatch(
            uploadContentFile({
              formData: formDataUpload,
              onProgress: (progress) => setUploadProgress(progress)
            })
          ).unwrap();
        }

        setFormData((prev) => ({
          ...prev,
          fileUrl: result.data.fileUrl,
          fileSize: result.data.fileSize || file.size,
        }));

        // Clear file upload validation error
        const errors = { ...validationErrors };
        delete errors.fileUpload;
        setValidationErrors(errors);

        showSuccess("Content file uploaded successfully!");
      } catch (error) {
        console.error("File upload failed:", error);
        setUploadedFile(null);
        setUploadError(error.message || "Failed to upload content file. Please try again.");
        setCanRetry(true);
        showError(error.message || "Failed to upload content file. Please try again.");
      } finally {
        setIsRetrying(false);
        if (!uploadError) {
          // Only hide progress if no error occurred
          setShowUploadProgress(false);
          setCurrentUploadType("");
          setCurrentFileName("");
          setUploadProgress(0);
          setUploadStats({});
        }
      }
    }
  };

  // Handle upload retry
  const handleUploadRetry = async () => {
    if (!currentUploadId) {
      // Restart the upload from beginning
      const fileInput = document.getElementById('file-upload');
      if (fileInput && fileInput.files[0]) {
        handleFileUpload({ target: fileInput });
      }
      return;
    }

    setIsRetrying(true);
    setUploadError(null);

    try {
      const result = await chunkedUploadService.resumeUpload(currentUploadId);

      setFormData((prev) => ({
        ...prev,
        fileUrl: result.data.fileUrl,
        fileSize: result.data.fileSize,
      }));

      // Clear file upload validation error
      const errors = { ...validationErrors };
      delete errors.fileUpload;
      setValidationErrors(errors);

      showSuccess("Content file uploaded successfully!");
      setShowUploadProgress(false);
      setCurrentUploadType("");
      setCurrentFileName("");
      setUploadProgress(0);
      setUploadStats({});
      setCanRetry(false);
    } catch (error) {
      console.error("Upload retry failed:", error);
      setUploadError(error.message || "Retry failed. Please try again.");
      showError(error.message || "Retry failed. Please try again.");
    } finally {
      setIsRetrying(false);
    }
  };

  // Handle upload cancel
  const handleUploadCancel = async () => {
    if (currentUploadId) {
      try {
        await chunkedUploadService.cancelUpload(currentUploadId);
        showSuccess("Upload cancelled and chunks cleaned up.");
      } catch (error) {
        console.error('Error cancelling upload:', error);
        showError("Upload cancelled locally, but server cleanup may have failed.");
      }
    }

    setShowUploadProgress(false);
    setCurrentUploadType("");
    setCurrentFileName("");
    setUploadProgress(0);
    setUploadStats({});
    setUploadError(null);
    setCanRetry(false);
    setCurrentUploadId(null);
    setUploadedFile(null);

    // Clear the file input
    const fileInput = document.getElementById('file-upload');
    if (fileInput) {
      fileInput.value = "";
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Set form as submitted to trigger validation display
    setFormSubmitted(true);

    // Validate all fields before submission
    const allErrors = {};

    // Validate required fields
    if (!formData.title.trim()) {
      allErrors.title = "Strategy title is required";
    } else if (formData.title.length > VALIDATION_LIMITS.TITLE.maxChars) {
      allErrors.title = `Title cannot exceed ${VALIDATION_LIMITS.TITLE.maxChars} characters`;
    }

    if (!formData.category) {
      allErrors.category = "Please select a category";
    }

    if (!formData.coachName.trim()) {
      allErrors.coachName = "Coach/Seller/Academy name is required";
    } else if (
      formData.coachName.length > VALIDATION_LIMITS.COACH_NAME.maxChars
    ) {
      allErrors.coachName = `Coach name cannot exceed ${VALIDATION_LIMITS.COACH_NAME.maxChars} characters`;
    }

    // Validate rich text fields - only required validation, no word count limits
    const cleanDescription = formData.description.replace(/<[^>]*>/g, '').trim();
    if (!cleanDescription) {
      allErrors.description = 'Strategy description is required';
    }

    if (!formData.fileUrl && !uploadedFile) {
      allErrors.fileUpload = "Please upload a video or document file";
    }

    const cleanAboutCoach = formData.aboutCoach.replace(/<[^>]*>/g, "").trim();
    if (!cleanAboutCoach) {
      allErrors.aboutCoach = 'About the coach information is required';
    }

    const cleanStrategicContent = formData.strategicContent
      .replace(/<[^>]*>/g, "")
      .trim();
    if (!cleanStrategicContent) {
      allErrors.strategicContent = 'Strategic content description is required';
    }

    if (!formData.contentType) {
      allErrors.contentType = "Please select a content type";
    }

    if (!formData.thumbnailUrl) {
      allErrors.thumbnailUpload = "Please upload a thumbnail image";
    }

    if (!formData.difficulty) {
      allErrors.difficulty = "Please select a difficulty level";
    }

    if (!formData.saleType) {
      allErrors.saleType = "Please select a sale type";
    }

    // Conditional price validation based on sale type
    if (formData.saleType === "Fixed") {
      if (!formData.price || formData.price <= 0) {
        allErrors.price = "Please enter a valid price greater than $0";
      }
    } else if (formData.saleType === "Auction") {
      // For auction, validate auction details instead of price
      if (!formData.auctionDetails.basePrice || formData.auctionDetails.basePrice <= 0) {
        allErrors.auctionBasePrice = "Please enter a valid starting bid price greater than $0";
      }
      if (!formData.auctionDetails.minimumBidIncrement || formData.auctionDetails.minimumBidIncrement <= 0) {
        allErrors.auctionMinIncrement = "Please enter a valid minimum bid increment greater than $0";
      }
      if (!formData.auctionDetails.auctionStartDate) {
        allErrors.auctionStartDate = "Please select an auction start date";
      } else {
        // Validate that start date is in the future
        const startDate = new Date(formData.auctionDetails.auctionStartDate);
        const now = new Date();
        if (startDate <= now) {
          allErrors.auctionStartDate = "Auction start date must be in the future";
        }
      }
      if (!formData.auctionDetails.auctionEndDate) {
        allErrors.auctionEndDate = "Please select an auction end date";
      }
      // Validate that end date is after start date
      if (formData.auctionDetails.auctionStartDate && formData.auctionDetails.auctionEndDate) {
        const startDate = new Date(formData.auctionDetails.auctionStartDate);
        const endDate = new Date(formData.auctionDetails.auctionEndDate);
        if (endDate <= startDate) {
          allErrors.auctionDateRange = "Auction end date must be after start date";
        }
      }
    }

    // If there are validation errors, show them and stop submission
    if (Object.keys(allErrors).length > 0) {
      // Force immediate state updates using flushSync
      flushSync(() => {
        setValidationErrors(allErrors);
        setShowValidation(true);
      });

      // Scroll to the first error field after DOM updates
      setTimeout(() => {
        const firstErrorField = document.querySelector(
          ".AddStrategy__validation-error"
        );
        if (firstErrorField) {
          firstErrorField.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }, 100);

      return;
    }

    try {
      // Map category to sport for backend compatibility since sport field is hidden
      const submitData = {
        ...formData,
        sport: formData.category || "Other", // Use category as sport for backend
        coachName: formData.coachName || "Coach", // Ensure coachName is included
      };

      // Handle conditional pricing based on sale type
      if (formData.saleType === "Fixed") {
        // For fixed price, use the price field
        submitData.price = parseFloat(formData.price);
      } else if (formData.saleType === "Auction") {
        // For auction, use the starting bid price as the effective price
        submitData.price = parseFloat(formData.auctionDetails.basePrice);

        // Convert auction dates to UTC before submission
        if (submitData.auctionDetails.auctionStartDate) {
          submitData.auctionDetails.auctionStartDate = toUTC(new Date(submitData.auctionDetails.auctionStartDate));
        }
        if (submitData.auctionDetails.auctionEndDate) {
          submitData.auctionDetails.auctionEndDate = toUTC(new Date(submitData.auctionDetails.auctionEndDate));
        }
      }

      const result = await dispatch(createContent(submitData)).unwrap();

      // Capture the created content ID for preview status tracking
      if (result.data && result.data._id) {
        setCreatedContentId(result.data._id);
        setShowPreviewStatus(true);
      }

      // Show the submission modal instead of navigating immediately
      setSubmittedContentTitle(formData.title);
      setShowSubmissionModal(true);

      // Still show a toast notification
      showSuccess(
        "🎉 Strategy created successfully! Preview will be generated in the background."
      );

      // Don't reset form immediately - let the modal handle it
    } catch (error) {
      console.error("Content creation failed:", error);

      // Error handling with specific messages
      let errorMessage = "Failed to create strategy. Please try again.";

      if (error.message) {
        errorMessage = error.message;
      } else if (error.errors && error.errors.length > 0) {
        errorMessage = error.errors[0].msg || errorMessage;
      } else if (typeof error === "string") {
        errorMessage = error;
      }

      // Error toast using custom toast utility
      showError(`❌ ${errorMessage}`);
    }
  };

  // Handle form reset
  const handleReset = () => {
    setFormData({
      // Visible fields
      title: "",
      category: "",
      coachName: "",
      description: "",
      fileUrl: "",
      aboutCoach: "",
      strategicContent: "",

      // Hidden fields with default values for backend compatibility
      sport: "Other",
      contentType: "",
      previewUrl: "",
      thumbnailUrl: "",
      duration: "",
      videoLength: "",
      fileSize: "",
      tags: [],
      difficulty: "",
      language: "English",
      prerequisites: [],
      learningObjectives: [],
      equipment: [],
      saleType: "",
      price: "",
      allowCustomRequests: false,
      customRequestPrice: "",
      status: "Draft",
      visibility: "Public",

      // Auction-specific fields
      auctionDetails: {
        basePrice: "",
        auctionStartDate: "",
        auctionEndDate: "",
        minimumBidIncrement: "",
        allowOfferBeforeAuctionStart: false,
      },
    });
    setUploadedFile(null);
    setCreatedContentId(null);
    setShowPreviewStatus(false);
    setValidationErrors({});
    setShowValidation(false);
    setFormSubmitted(false);
    setThumbnailError("");
    setShowThumbnailPreview(false);
    setShowSubmissionModal(false);
    setSubmittedContentTitle("");
    // Array field resets - Hidden to match UI screenshot
    // setNewTag("");
    // setNewPrerequisite("");
    // setNewLearningObjective("");
    // setNewEquipment("");
  };

  // Handle modal close and navigation
  const handleModalClose = () => {
    setShowSubmissionModal(false);
    // Reset form after modal is closed
    handleReset();
    // Navigate to strategies page
    navigate("/seller/my-sports-strategies");
  };

  // Add these constants at the top of the file
  const THUMBNAIL_LIMITS = {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif']
  };

  // Update the handleThumbnailUpload function
  const handleThumbnailUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Clear previous errors
    setThumbnailError("");
    setShowThumbnailPreview(false);

    try {
      // Validate file type
      if (!THUMBNAIL_LIMITS.allowedTypes.includes(file.type)) {
        throw new Error(
          "Only JPG, JPEG, PNG, and GIF formats are supported for thumbnails"
        );
      }

      // Validate file size
      if (file.size > THUMBNAIL_LIMITS.maxSize) {
        throw new Error("Thumbnail file size must be less than 5MB");
      }

      // Show upload progress
      setShowUploadProgress(true);
      setCurrentUploadType("thumbnail");
      setCurrentFileName(file.name);
      setUploadProgress(0); // Reset progress

      const formDataUpload = new FormData();
      formDataUpload.append("file", file);
      formDataUpload.append("type", "thumbnail");

      const response = await dispatch(
        uploadContentFile({
          formData: formDataUpload,
          onProgress: (progress) => setUploadProgress(progress)
        })
      ).unwrap();

      if (!response.data || !response.data.fileUrl) {
        throw new Error("Invalid response from server");
      }

      // Store the complete URL path
      const thumbnailUrl = response.data.fileUrl;
      // Update form data with the new thumbnail URL
      setFormData((prev) => ({
        ...prev,
        thumbnailUrl: thumbnailUrl,
      }));

      setShowThumbnailPreview(true);
      showSuccess("Thumbnail uploaded successfully!");
    } catch (error) {
      console.error("Thumbnail upload failed:", error);
      setThumbnailError(
        error.message || "Failed to upload thumbnail. Please try again."
      );
      setFormData((prev) => ({
        ...prev,
        thumbnailUrl: "",
      }));
    } finally {
      setShowUploadProgress(false);
      setCurrentUploadType("");
      setCurrentFileName("");
      setUploadProgress(0); // Reset progress
    }
  };

  return (
    <SellerLayout>
      <div className="AddStrategy">
        {/* Main Form */}
        <form className="AddStrategy__form" onSubmit={handleSubmit}>
          {/* Strategy Title */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Strategy Title</label>
            <input
              type="text"
              name="title"
              className="AddStrategy__input"
              placeholder="Add title for New Strategy"
              value={formData.title}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            />
            {(validationErrors.title ||
              (formSubmitted && !formData.title.trim())) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.title || "Strategy title is required"}
                  </p>
                </div>
              )}
          </div>

          {/* Select Category */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Select Category</label>
            <select
              name="category"
              className="AddStrategy__select"
              value={formData.category}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            >
              <option value="">Select Category</option>
              <option value="Basketball">Basketball</option>
              <option value="Football">Football</option>
              <option value="Soccer">Soccer</option>
              <option value="Baseball">Baseball</option>
            </select>
            {(validationErrors.category ||
              (formSubmitted && !formData.category)) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.category || "Please select a category"}
                  </p>
                </div>
              )}
          </div>
          {/* Coach/Seller/Academy Name */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">
              Coach/Seller/Academy Name
            </label>
            <input
              type="text"
              name="coachName"
              className="AddStrategy__input"
              placeholder="Enter coach, seller, or academy name"
              value={formData.coachName}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            />
            {(validationErrors.coachName ||
              (showValidation && !formData.coachName.trim())) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.coachName ||
                      "Coach/Seller/Academy name is required"}
                  </p>
                </div>
              )}
          </div>

          {/* Hidden Sport Field - Using category as sport for backend compatibility */}
          {/* Sport field is hidden to match UI, using category value for sport */}

          {/* Description for Strategy - Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">
              Description for Strategy
            </label>
            <SummernoteEditor
              value={formData.description}
              onChange={(value) => handleSummernoteChange("description", value)}
              placeholder="Enter a detailed description of your strategy..."
              height={200}
              className="AddStrategy__summernote"
            />

            {(validationErrors.description ||
              (formSubmitted &&
                !formData.description.replace(/<[^>]*>/g, "").trim())) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.description ||
                      "Strategy description is required"}
                  </p>
                </div>
              )}
          </div>
          {/* Content Type */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Content Type</label>
            <select
              name="contentType"
              className="AddStrategy__select"
              value={formData.contentType}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            >
              <option value="">Select Content Type</option>
              <option value="Video">Video</option>
              <option value="Document">Document</option>
              {/* <option value="Audio">Audio</option>
              <option value="Image">Image</option>
              <option value="Text">Text</option> */}
            </select>
            {(validationErrors.contentType ||
              (formSubmitted && !formData.contentType)) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.contentType ||
                      "Please select a content type"}
                  </p>
                </div>
              )}
          </div>
          {/* File Upload */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">
              Upload {formData.contentType || "Video/Document"}
            </label>
            {formData.contentType && (
              <p className="AddStrategy__format-note">
                {formData.contentType === 'Video' ? (
                  <>Maximum size: <span>{formatFileSize(FILE_SIZE_LIMITS.Video)}</span> • Supported formats: <span>MP4, MOV, AVI, WEBM</span></>
                ) : formData.contentType === 'Document' ? (
                  <>Maximum size: <span>{formatFileSize(FILE_SIZE_LIMITS.Document)}</span> • Supported formats: <span>PDF</span></>
                ) : (
                  'Please select a content type to see upload requirements'
                )}
              </p>
            )}
            <label
              htmlFor="file-upload"
              className="AddStrategy__upload"
            >
              <input
                type="file"
                id="file-upload"
                className="AddStrategy__file-input"
                onChange={handleFileUpload}
                accept={getAcceptAttribute(formData.contentType)}
                disabled={isFileUploadDisabled(formData.contentType)}
                style={{ display: "none" }}
              />
              <div
                className={`AddStrategy__upload-content ${isFileUploadDisabled(formData.contentType)
                  ? "AddStrategy__upload-content--disabled"
                  : ""
                  }`}
              >
                <FiUpload className="AddStrategy__upload-icon" />
                <p className="AddStrategy__upload-text">
                  {uploadedFile
                    ? uploadedFile.name
                    : formData.fileUrl
                      ? "Current file uploaded"
                      : "Click to upload file"}
                </p>
              </div>

              {/* File info display */}
              {(uploadedFile || formData.fileUrl) && (
                <div className="AddStrategy__file-info">
                  <p className="AddStrategy__file-name">
                    {uploadedFile ? uploadedFile.name : "Current file uploaded"}
                  </p>
                  {uploadedFile && (
                    <p className="AddStrategy__file-size">
                      {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  )}
                </div>
              )}
            </label>



            {/* Validation error message */}
            {(validationErrors.fileUpload ||
              (formSubmitted && !formData.fileUrl && !uploadedFile)) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.fileUpload ||
                      "Please upload a video or document file"}
                  </p>
                </div>
              )}
          </div>

          {/* About The Coach - Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">About The Coach</label>
            <SummernoteEditor
              value={formData.aboutCoach}
              onChange={(value) => handleSummernoteChange("aboutCoach", value)}
              placeholder="Share your background, experience, and expertise..."
              height={200}
              className="AddStrategy__summernote"
            />

            {(validationErrors.aboutCoach ||
              (formSubmitted &&
                !formData.aboutCoach.replace(/<[^>]*>/g, "").trim())) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.aboutCoach ||
                      "About the coach information is required"}
                  </p>
                </div>
              )}
          </div>

          {/* Includes Strategic Content - Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">
              Includes Strategic Content
            </label>
            <SummernoteEditor
              value={formData.strategicContent}
              onChange={(value) =>
                handleSummernoteChange("strategicContent", value)
              }
              placeholder="Describe what strategic content is included..."
              height={200}
              className="AddStrategy__summernote"
            />

            {(validationErrors.strategicContent ||
              (formSubmitted &&
                !formData.strategicContent.replace(/<[^>]*>/g, "").trim())) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.strategicContent ||
                      "Strategic content description is required"}
                  </p>
                </div>
              )}
          </div>

          {/* Thumbnail Upload */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Thumbnail Image</label>
            <p className="AddStrategy__format-note">
              Maximum size: <span>5MB</span> • Supported formats: <span>JPG, JPEG, PNG, GIF</span>
            </p>
            <label
              htmlFor="thumbnail-upload"
              className="AddStrategy__upload"
            >
              <input
                type="file"
                id="thumbnail-upload"
                className="AddStrategy__file-input"
                accept="image/jpeg,image/jpg,image/png,image/gif"
                onChange={handleThumbnailUpload}
                style={{ display: "none" }}
              />
              <div className="AddStrategy__upload-content">
                <FiUpload className="AddStrategy__upload-icon" />
                <p className="AddStrategy__upload-text">
                  {formData.thumbnailUrl
                    ? "Thumbnail uploaded"
                    : "Click to upload thumbnail"}
                </p>
              </div>

              {/* Thumbnail validation error */}
              {thumbnailError && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">{thumbnailError}</p>
                </div>
              )}

              {/* Required field validation error */}
              {(validationErrors.thumbnailUpload ||
                (formSubmitted && !formData.thumbnailUrl)) && (
                  <div className="AddStrategy__validation-error">
                    <p className="AddStrategy__error-message">
                      {validationErrors.thumbnailUpload ||
                        "Please upload a thumbnail image"}
                    </p>
                  </div>
                )}

              {/* Thumbnail preview */}
              {formData.thumbnailUrl && showThumbnailPreview && (
                <div className="AddStrategy__thumbnail-preview">
                  <img
                    src={getImageUrl(formData.thumbnailUrl)}
                    alt="Thumbnail preview"
                    onError={(e) => {
                      console.error("Thumbnail preview failed:", e);
                      console.error(
                        "Failed URL:",
                        getImageUrl(formData.thumbnailUrl)
                      );
                      console.error(
                        "Original thumbnailUrl:",
                        formData.thumbnailUrl
                      );

                      // Show placeholder image
                      e.target.src = getPlaceholderImage(
                        200,
                        120,
                        "Image not found"
                      );
                    }}
                    style={{
                      maxWidth: "100%",
                      height: "auto",
                      borderRadius: "var(--border-radius)",
                    }}
                  />
                </div>
              )}

              {/* Show placeholder when no thumbnail URL */}
              {!formData.thumbnailUrl && (
                <div className="AddStrategy__thumbnail-preview">
                  <img
                    src={getPlaceholderImage(200, 120, "No thumbnail")}
                    alt="No thumbnail"
                    style={{
                      maxWidth: "100%",
                      height: "auto",
                      borderRadius: "var(--border-radius)",
                      opacity: 0.7,
                    }}
                  />
                </div>
              )}
            </label>
          </div>

          {/* Tags */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Tags</label>
            <div className="AddStrategy__array-field">
              <div className="AddStrategy__array-input">
                <input
                  type="text"
                  className="AddStrategy__input"
                  placeholder="Add a tag (e.g., basketball, technique, training)..."
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      if (newTag.trim()) {
                        setFormData((prev) => ({
                          ...prev,
                          tags: [...prev.tags, newTag.trim()],
                        }));
                        setNewTag("");
                      }
                    }
                  }}
                />
                <button
                  type="button"
                  className="AddStrategy__add-btn btn-primary"
                  onClick={() => {
                    if (newTag.trim()) {
                      setFormData((prev) => ({
                        ...prev,
                        tags: [...prev.tags, newTag.trim()],
                      }));
                      setNewTag("");
                    }
                  }}
                >
                  Add
                </button>
              </div>
              <div className="AddStrategy__array-items">
                {formData.tags.map((tag, index) => (
                  <div key={index} className="AddStrategy__array-item">
                    <span>{tag}</span>
                    <button
                      type="button"
                      className="AddStrategy__remove-btn"
                      onClick={() => {
                        setFormData((prev) => ({
                          ...prev,
                          tags: prev.tags.filter((_, i) => i !== index),
                        }));
                      }}
                    >
                      X
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Difficulty Level */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Difficulty Level</label>
            <select
              name="difficulty"
              className="AddStrategy__select"
              value={formData.difficulty}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            >
              <option value="">Select Difficulty</option>
              <option value="Beginner">Beginner</option>
              <option value="Intermediate">Intermediate</option>
              <option value="Advanced">Advanced</option>
              <option value="Professional">Professional</option>
            </select>
            {(validationErrors.difficulty ||
              (formSubmitted && !formData.difficulty)) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.difficulty ||
                      "Please select a difficulty level"}
                  </p>
                </div>
              )}
          </div>

          {/* Sale Type */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Sale Type</label>
            <select
              name="saleType"
              className="AddStrategy__select"
              value={formData.saleType}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            >
              <option value="">Select Sale Type</option>
              <option value="Fixed">Fixed Price</option>
              <option value="Auction">Auction</option>
            </select>
            {(validationErrors.saleType ||
              (formSubmitted && !formData.saleType)) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.saleType || "Please select a sale type"}
                  </p>
                </div>
              )}
          </div>

          {/* Price - Only show for Fixed sale type */}
          {formData.saleType === "Fixed" && (
            <div className="AddStrategy__field">
              <label className="AddStrategy__label">Price ($)</label>
              <input
                type="number"
                name="price"
                className="AddStrategy__input"
                placeholder="Enter price"
                value={formData.price}
                onChange={handleInputChange}
                onBlur={handleFieldBlur}
                min="0"
                step="0.01"
              />
              {(validationErrors.price ||
                (formSubmitted && (!formData.price || formData.price <= 0))) && (
                  <div className="AddStrategy__validation-error">
                    <p className="AddStrategy__error-message">
                      {validationErrors.price ||
                        "Please enter a valid price greater than $0"}
                    </p>
                  </div>
                )}
            </div>
          )}

          {/* Auction Fields - Show only when Auction is selected */}
          {formData.saleType === "Auction" && (
            <div className="AddStrategy__auction-section">
              <h3 className="AddStrategy__section-title">Auction Settings</h3>

              {/* Base Price */}
              <div className="AddStrategy__field">
                <label className="AddStrategy__label">
                  Starting Bid Price ($)
                </label>
                <input
                  type="number"
                  name="auctionDetails.basePrice"
                  className="AddStrategy__input"
                  placeholder="Enter starting bid price"
                  value={formData.auctionDetails.basePrice}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      auctionDetails: {
                        ...prev.auctionDetails,
                        basePrice: e.target.value,
                      },
                    }))
                  }
                  min="0"
                  step="0.01"
                />
                {(validationErrors.auctionBasePrice ||
                  (formSubmitted && (!formData.auctionDetails.basePrice || formData.auctionDetails.basePrice <= 0))) && (
                    <div className="AddStrategy__validation-error">
                      <p className="AddStrategy__error-message">
                        {validationErrors.auctionBasePrice || "Please enter a valid starting bid price greater than $0"}
                      </p>
                    </div>
                  )}
              </div>

              {/* Minimum Bid Increment */}
              <div className="AddStrategy__field">
                <label className="AddStrategy__label">
                  Minimum Bid Increment ($)
                </label>
                <input
                  type="number"
                  name="auctionDetails.minimumBidIncrement"
                  className="AddStrategy__input"
                  placeholder="Enter minimum bid increment"
                  value={formData.auctionDetails.minimumBidIncrement}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      auctionDetails: {
                        ...prev.auctionDetails,
                        minimumBidIncrement: e.target.value,
                      },
                    }))
                  }
                  min="0.01"
                  step="0.01"
                />
                {(validationErrors.auctionMinIncrement ||
                  (formSubmitted && (!formData.auctionDetails.minimumBidIncrement || formData.auctionDetails.minimumBidIncrement <= 0))) && (
                    <div className="AddStrategy__validation-error">
                      <p className="AddStrategy__error-message">
                        {validationErrors.auctionMinIncrement || "Please enter a valid minimum bid increment greater than $0"}
                      </p>
                    </div>
                  )}
              </div>

              {/* Auction Start Date */}
              <div className="AddStrategy__field">
                <label className="AddStrategy__label">Auction Start Date</label>
                <TimezoneErrorBoundary>
                  <input
                    type="datetime-local"
                    name="auctionDetails.auctionStartDate"
                    className="AddStrategy__input"
                    value={formData.auctionDetails.auctionStartDate}
                    min={new Date().toISOString().slice(0, 16)}
                    onChange={(e) => {
                      const value = e.target.value;
                      setFormData((prev) => ({
                        ...prev,
                        auctionDetails: {
                          ...prev.auctionDetails,
                          auctionStartDate: value,
                        },
                      }));

                      // Real-time validation using UTC comparison
                      if (value) {
                        const startDate = toUTC(new Date(value));
                        const now = new Date();
                        if (startDate <= now) {
                          setValidationErrors(prev => ({
                            ...prev,
                            auctionStartDate: "Auction start date must be in the future"
                          }));
                        } else {
                          setValidationErrors(prev => {
                            const newErrors = { ...prev };
                            delete newErrors.auctionStartDate;
                            return newErrors;
                          });
                        }
                      }
                    }}
                  />
                  <TimezoneInfo />
                </TimezoneErrorBoundary>
                {(validationErrors.auctionStartDate ||
                  (formSubmitted && !formData.auctionDetails.auctionStartDate)) && (
                    <div className="AddStrategy__validation-error">
                      <p className="AddStrategy__error-message">
                        {validationErrors.auctionStartDate || "Please select an auction start date"}
                      </p>
                    </div>
                  )}
              </div>

              {/* Auction End Date */}
              <div className="AddStrategy__field">
                <label className="AddStrategy__label">Auction End Date</label>
                <TimezoneErrorBoundary>
                  <input
                    type="datetime-local"
                    name="auctionDetails.auctionEndDate"
                    className="AddStrategy__input"
                    value={formData.auctionDetails.auctionEndDate}
                    min={formData.auctionDetails.auctionStartDate ? new Date(formData.auctionDetails.auctionStartDate).toISOString().slice(0, 16) : new Date().toISOString().slice(0, 16)}
                    onChange={(e) => {
                      const value = e.target.value;
                      setFormData((prev) => ({
                        ...prev,
                        auctionDetails: {
                          ...prev.auctionDetails,
                          auctionEndDate: value,
                        },
                      }));

                      // Real-time validation using UTC comparison
                      if (value && formData.auctionDetails.auctionStartDate) {
                        const startDate = toUTC(new Date(formData.auctionDetails.auctionStartDate));
                        const endDate = toUTC(new Date(value));
                        if (endDate <= startDate) {
                          setValidationErrors(prev => ({
                            ...prev,
                            auctionDateRange: "Auction end date must be after start date"
                          }));
                        } else {
                          setValidationErrors(prev => {
                            const newErrors = { ...prev };
                            delete newErrors.auctionDateRange;
                            return newErrors;
                          });
                        }
                      }
                    }}
                  />
                  <TimezoneInfo />
                </TimezoneErrorBoundary>
                {(validationErrors.auctionEndDate || validationErrors.auctionDateRange ||
                  (formSubmitted && !formData.auctionDetails.auctionEndDate)) && (
                    <div className="AddStrategy__validation-error">
                      <p className="AddStrategy__error-message">
                        {validationErrors.auctionEndDate || validationErrors.auctionDateRange || "Please select an auction end date"}
                      </p>
                    </div>
                  )}
              </div>

              {/* Allow Offers Before Auction Start */}
              <div className="AddStrategy__field">
                <label className="AddStrategy__checkbox-label">
                  <input
                    type="checkbox"
                    name="auctionDetails.allowOfferBeforeAuctionStart"
                    checked={
                      formData.auctionDetails.allowOfferBeforeAuctionStart
                    }
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        auctionDetails: {
                          ...prev.auctionDetails,
                          allowOfferBeforeAuctionStart: e.target.checked,
                        },
                      }))
                    }
                    className="AddStrategy__checkbox"
                  />
                  Allow offers before auction starts
                </label>
              </div>

              {/* Auction Note */}
              <div className="AddStrategy__field">
                <div className="AddStrategy__auction-note">
                  <p>
                    <strong>Note:</strong> Once the auction starts, the strategy content cannot be edited.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Allow Custom Requests */}
          {/* <div className="AddStrategy__field">
            <label className="AddStrategy__checkbox-label">
              <input
                type="checkbox"
                name="allowCustomRequests"
                checked={formData.allowCustomRequests}
                onChange={handleInputChange}
                className="AddStrategy__checkbox"
              />
              Allow Custom Requests
            </label>
          </div> */}

          {/* Language - Hidden to match UI screenshot */}
          {/* <div className="AddStrategy__field">
            <label className="AddStrategy__label">Language</label>
            <select
              name="language"
              className="AddStrategy__select"
              value={formData.language}
              onChange={handleInputChange}
            >
              <option value="English">English</option>
              <option value="Spanish">Spanish</option>
              <option value="French">French</option>
              <option value="German">German</option>
              <option value="Italian">Italian</option>
              <option value="Portuguese">Portuguese</option>
              <option value="Chinese">Chinese</option>
              <option value="Japanese">Japanese</option>
              <option value="Korean">Korean</option>
              <option value="Other">Other</option>
            </select>
          </div> */}

          {/* Prerequisites - Hidden to match UI screenshot */}
          {/* <div className="AddStrategy__field">
            <label className="AddStrategy__label">Prerequisites</label>
            <div className="AddStrategy__array-field">
              <div className="AddStrategy__array-input">
                <input
                  type="text"
                  className="AddStrategy__input"
                  placeholder="Add a prerequisite..."
                  value={newPrerequisite}
                  onChange={(e) => setNewPrerequisite(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addArrayItem('prerequisites', newPrerequisite, setNewPrerequisite))}
                />
                <button
                  type="button"
                  className="AddStrategy__add-btn"
                  onClick={() => addArrayItem('prerequisites', newPrerequisite, setNewPrerequisite)}
                >
                  <FiPlus />
                </button>
              </div>
              <div className="AddStrategy__array-items">
                {formData.prerequisites.map((item, index) => (
                  <div key={index} className="AddStrategy__array-item">
                    <span>{item}</span>
                    <button
                      type="button"
                      className="AddStrategy__remove-btn"
                      onClick={() => removeArrayItem('prerequisites', index)}
                    >
                      <FiX />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div> */}

          {/* Learning Objectives - Hidden to match UI screenshot */}
          {/* <div className="AddStrategy__field">
            <label className="AddStrategy__label">Learning Objectives</label>
            <div className="AddStrategy__array-field">
              <div className="AddStrategy__array-input">
                <input
                  type="text"
                  className="AddStrategy__input"
                  placeholder="Add a learning objective..."
                  value={newLearningObjective}
                  onChange={(e) => setNewLearningObjective(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addArrayItem('learningObjectives', newLearningObjective, setNewLearningObjective))}
                />
                <button
                  type="button"
                  className="AddStrategy__add-btn"
                  onClick={() => addArrayItem('learningObjectives', newLearningObjective, setNewLearningObjective)}
                >
                  <FiPlus />
                </button>
              </div>
              <div className="AddStrategy__array-items">
                {formData.learningObjectives.map((item, index) => (
                  <div key={index} className="AddStrategy__array-item">
                    <span>{item}</span>
                    <button
                      type="button"
                      className="AddStrategy__remove-btn"
                      onClick={() => removeArrayItem('learningObjectives', index)}
                    >
                      <FiX />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div> */}

          {/* Equipment - Hidden to match UI screenshot */}
          {/* <div className="AddStrategy__field">
            <label className="AddStrategy__label">Required Equipment</label>
            <div className="AddStrategy__array-field">
              <div className="AddStrategy__array-input">
                <input
                  type="text"
                  className="AddStrategy__input"
                  placeholder="Add required equipment..."
                  value={newEquipment}
                  onChange={(e) => setNewEquipment(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addArrayItem('equipment', newEquipment, setNewEquipment))}
                />
                <button
                  type="button"
                  className="AddStrategy__add-btn"
                  onClick={() => addArrayItem('equipment', newEquipment, setNewEquipment)}
                >
                  <FiPlus />
                </button>
              </div>
              <div className="AddStrategy__array-items">
                {formData.equipment.map((item, index) => (
                  <div key={index} className="AddStrategy__array-item">
                    <span>{item}</span>
                    <button
                      type="button"
                      className="AddStrategy__remove-btn"
                      onClick={() => removeArrayItem('equipment', index)}
                    >
                      <FiX />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div> */}

          {/* Video Length - Hidden to match UI screenshot */}
          {/* <div className="AddStrategy__field">
            <label className="AddStrategy__label">Video Length (minutes)</label>
            <input
              type="number"
              name="videoLength"
              className="AddStrategy__input"
              placeholder="Enter video length in minutes"
              value={formData.videoLength}
              onChange={handleInputChange}
              min="0"
            />
          </div> */}

          {/* Upload Section - This was moved up to match screenshot order */}

          {/* Error Display */}
          {isError && error && (
            <div className="AddStrategy__error">
              <p>Error: {error.message || "Something went wrong"}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="AddStrategy__actions">
            <button type="submit" className="btn-primary" disabled={isLoading}>
              {isLoading ? "Creating..." : "Add New Strategy"}
            </button>
            <button
              type="button"
              className="btn-outline"
              onClick={handleReset}
              disabled={isLoading}
            >
              Reset Form
            </button>
          </div>
        </form>

        {/* Preview Status Indicator */}
        {showPreviewStatus && createdContentId && (
          <PreviewStatusIndicator
            contentId={createdContentId}
            onStatusChange={(status) => {
              console.log("Preview status updated:", status);
            }}
          />
        )}

        {/* Upload Progress Bar */}
        <UploadProgressBar
          isVisible={showUploadProgress}
          progress={uploadProgress}
          fileName={currentFileName}
          uploadType={currentUploadType}
          uploadStats={uploadStats}
          error={uploadError}
          isRetrying={isRetrying}
          canRetry={canRetry}
          canCancel={true}
          onRetry={handleUploadRetry}
          onCancel={handleUploadCancel}
        />

        {/* Content Submission Modal */}
        <ContentSubmissionModal
          isOpen={showSubmissionModal}
          onClose={handleModalClose}
          contentTitle={submittedContentTitle}
        />
      </div>
    </SellerLayout>
  );
};

export default AddStrategy;
