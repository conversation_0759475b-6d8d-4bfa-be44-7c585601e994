import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectSettings,
  selectLoading,
  selectErrors,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import {
  fetchSettings,
  updateSettings,
} from "../../redux/slices/adminDashboardThunks";
import { showSuccess, showError } from "../../utils/toast";
import { fetchPublicSettings, fetchPlatformCommission } from "../../redux/slices/settingsSlice";
import { IMAGE_BASE_URL, STORAGE_KEYS, API_BASE_URL } from "../../utils/constants";
import AdminLayout from "../../components/admin/AdminLayout";
import "../../styles/AdminSettings.css";


// Icons
import {
  FaSave,
  FaUndo,
  FaCog,
  FaGlobe,
  FaDollarSign,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaExternalLinkAlt,
  FaFacebookF,
  FaTwitter,
  FaInstagram,
  FaPercentage,
} from "react-icons/fa";

const AdminSettings = () => {
  const dispatch = useDispatch();
  const settings = useSelector(selectSettings);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);

  // Form state
  const [formData, setFormData] = useState({
    general: {
      siteName: "",
      siteLogo: "",
      siteFavicon: "",
      contactEmail: "",
      contactPhone: "",
      address: "",
      supportLink: "",
      socialLinks: {
        facebook: "",
        twitter: "",
        instagram: ""
      }
    },
    financial: {
      platformCommissionPercentage: 5,
    },
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [isUploadingLogo, setIsUploadingLogo] = useState(false);
  const [isUploadingFavicon, setIsUploadingFavicon] = useState(false);

  // Fetch settings on component mount
  useEffect(() => {
    dispatch(fetchSettings());
  }, [dispatch]);

  // Update form data when settings are loaded
  useEffect(() => {
    if (settings) {
      setFormData({
        general: {
          siteName: settings.general?.siteName || "",
          siteLogo: settings.general?.siteLogo || "",
          siteFavicon: settings.general?.siteFavicon || "",
          contactEmail: settings.general?.contactEmail || "",
          contactPhone: settings.general?.contactPhone || "",
          address: settings.general?.address || "",
          supportLink: settings.general?.supportLink || "",
          socialLinks: {
            facebook: settings.general?.socialLinks?.facebook || "",
            twitter: settings.general?.socialLinks?.twitter || "",
            instagram: settings.general?.socialLinks?.instagram || ""
          }
        },
        financial: {
          platformCommissionPercentage: settings.financial?.platformCommissionPercentage || 5,
        },
      });
    }
  }, [settings]);

  // Handle input changes
  const handleInputChange = (section, field, value) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
    setHasChanges(true);
  };

  // Handle social media link changes
  const handleSocialLinkChange = (platform, value) => {
    setFormData(prev => ({
      ...prev,
      general: {
        ...prev.general,
        socialLinks: {
          ...prev.general.socialLinks,
          [platform]: value,
        },
      },
    }));
    setHasChanges(true);
  };

  // Clean form data by removing empty strings for optional fields
  const cleanFormData = (data) => {
    const cleaned = JSON.parse(JSON.stringify(data)); // Deep clone

    // Clean general settings
    if (cleaned.general) {
      // Remove empty strings for optional fields that require validation
      if (cleaned.general.contactEmail === '') {
        delete cleaned.general.contactEmail;
      }
      if (cleaned.general.supportLink === '') {
        delete cleaned.general.supportLink;
      }

      // Clean social links
      if (cleaned.general.socialLinks) {
        if (cleaned.general.socialLinks.facebook === '') {
          delete cleaned.general.socialLinks.facebook;
        }
        if (cleaned.general.socialLinks.twitter === '') {
          delete cleaned.general.socialLinks.twitter;
        }
        if (cleaned.general.socialLinks.instagram === '') {
          delete cleaned.general.socialLinks.instagram;
        }
      }
    }

    return cleaned;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const cleanedData = cleanFormData(formData);
      await dispatch(updateSettings(cleanedData)).unwrap();
      showSuccess("Settings updated successfully!");
      setHasChanges(false);

      // Refresh global settings to reflect changes across the app
      dispatch(fetchPublicSettings());
      dispatch(fetchPlatformCommission());

      // Add activity log
      dispatch(
        addActivity({
          id: Date.now(),
          type: "settings_update",
          description: "Platform settings updated",
          timestamp: new Date().toISOString(),
          user: "Admin",
        })
      );
    } catch (error) {
      showError(error.message || "Failed to update settings");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle logo upload
  const handleLogoUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      showError('Please select an image file');
      return;
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      showError('File size must be less than 5MB');
      return;
    }

    setIsUploadingLogo(true);

    try {
      const formData = new FormData();
      formData.append('logo', file);

      const response = await fetch(`${API_BASE_URL}/admin/settings/upload-logo`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload logo');
      }

      const data = await response.json();

      // Update form data with new logo path
      setFormData(prev => ({
        ...prev,
        general: {
          ...prev.general,
          siteLogo: data.data.siteLogo,
        },
      }));

      setHasChanges(true);
      showSuccess('Logo uploaded successfully!');

    } catch (error) {
      showError(error.message || 'Failed to upload logo');
    } finally {
      setIsUploadingLogo(false);
    }
  };

  // Handle favicon upload
  const handleFaviconUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      showError('Please select an image file');
      return;
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      showError('File size must be less than 5MB');
      return;
    }

    setIsUploadingFavicon(true);

    try {
      const formData = new FormData();
      formData.append('favicon', file);

      const response = await fetch(`${API_BASE_URL}/admin/settings/upload-favicon`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload favicon');
      }

      const data = await response.json();

      // Update form data with new favicon path
      setFormData(prev => ({
        ...prev,
        general: {
          ...prev.general,
          siteFavicon: data.data.siteFavicon,
        },
      }));

      setHasChanges(true);
      showSuccess('Favicon uploaded successfully!');

    } catch (error) {
      showError(error.message || 'Failed to upload favicon');
    } finally {
      setIsUploadingFavicon(false);
    }
  };

  // Handle reset
  const handleReset = () => {
    if (settings) {
      setFormData({
        general: {
          siteName: settings.general?.siteName || "",
          siteLogo: settings.general?.siteLogo || "",
          siteFavicon: settings.general?.siteFavicon || "",
          contactEmail: settings.general?.contactEmail || "",
          contactPhone: settings.general?.contactPhone || "",
          address: settings.general?.address || "",
          supportLink: settings.general?.supportLink || "",
          socialLinks: {
            facebook: settings.general?.socialLinks?.facebook || "",
            twitter: settings.general?.socialLinks?.twitter || "",
            instagram: settings.general?.socialLinks?.instagram || ""
          }
        },
        financial: {
          platformCommissionPercentage: settings.financial?.platformCommissionPercentage || 5,
        },
      });
      setHasChanges(false);
    }
  };

  return (
    <AdminLayout>
      <div className="AdminSettings">
        <div className="AdminSettings__header">
          <div className="header-left">
            <h1>
              <FaCog className="header-icon" />
              Platform Settings
            </h1>
            <p>Manage application-wide settings and configurations</p>
          </div>
          <div className="header-right">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={handleReset}
              disabled={!hasChanges || isSubmitting}
            >
              <FaUndo />
              Reset Changes
            </button>
            <button
              type="submit"
              form="settings-form"
              className="btn btn-primary"
              disabled={!hasChanges || isSubmitting}
            >
              <FaSave />
              {isSubmitting ? "Saving..." : "Save Settings"}
            </button>
          </div>
        </div>

        {loading.settings ? (
          <div className="AdminSettings__loading">
            <div className="loading-spinner"></div>
            <p>Loading settings...</p>
          </div>
        ) : (
          <form id="settings-form" onSubmit={handleSubmit} className="AdminSettings__form">
            {/* General Settings Section */}
            <div className="settings-section">
              <div className="section-header">
                <h2>
                  <FaGlobe className="section-icon" />
                  General Settings
                </h2>
                <p>Basic site information and configuration</p>
              </div>

              <div className="form-grid">
                <div className="form-group">
                  <label htmlFor="siteName">
                    <FaGlobe />
                    Site Name
                  </label>
                  <input
                    type="text"
                    id="siteName"
                    value={formData.general.siteName}
                    onChange={(e) => handleInputChange("general", "siteName", e.target.value)}
                    placeholder="Enter site name"
                    className="form-control"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="siteLogo">
                    <FaGlobe />
                    Site Logo
                  </label>
                  <div className="logo-upload-container">
                    {formData.general.siteLogo && (
                      <div className="current-logo">
                        <img
                          src={
                            formData.general.siteLogo && formData.general.siteLogo.startsWith('/uploads')
                              ? `${IMAGE_BASE_URL}${formData.general.siteLogo}`
                              : formData.general.siteLogo
                          }
                          alt="Current Logo"
                          className="logo-preview"
                        />
                      </div>
                    )}
                    <input
                      type="file"
                      id="siteLogo"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="form-control file-input"
                      disabled={isUploadingLogo}
                    />
                    {isUploadingLogo && (
                      <div className="upload-progress">
                        <div className="loading-spinner"></div>
                        <span>Uploading...</span>
                      </div>
                    )}
                  </div>
                  <small className="form-text">
                    Upload an image file (max 5MB). Supported formats: JPG, PNG, GIF, SVG
                  </small>
                </div>

                <div className="form-group">
                  <label htmlFor="siteFavicon">
                    <FaGlobe />
                    Site Favicon
                  </label>
                  <div className="logo-upload-container">
                    {formData.general.siteFavicon && (
                      <div className="current-logo">
                        <img
                          src={
                            formData.general.siteFavicon && formData.general.siteFavicon.startsWith('/uploads')
                              ? `${IMAGE_BASE_URL}${formData.general.siteFavicon}`
                              : formData.general.siteFavicon
                          }
                          alt="Current Favicon"
                          className="favicon-preview"
                          style={{ width: '32px', height: '32px' }}
                        />
                      </div>
                    )}
                    <input
                      type="file"
                      id="siteFavicon"
                      accept="image/*"
                      onChange={handleFaviconUpload}
                      className="form-control file-input"
                      disabled={isUploadingFavicon}
                    />
                    {isUploadingFavicon && (
                      <div className="upload-progress">
                        <div className="loading-spinner"></div>
                        <span>Uploading...</span>
                      </div>
                    )}
                  </div>
                  <small className="form-text">
                    Upload a favicon image (max 5MB). Recommended size: 32x32 or 16x16 pixels
                  </small>
                </div>

                <div className="form-group">
                  <label htmlFor="contactEmail">
                    <FaEnvelope />
                    Contact Email
                  </label>
                  <input
                    type="email"
                    id="contactEmail"
                    value={formData.general.contactEmail}
                    onChange={(e) => handleInputChange("general", "contactEmail", e.target.value)}
                    placeholder="Enter contact email"
                    className="form-control"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="contactPhone">
                    <FaPhone />
                    Contact Phone
                  </label>
                  <input
                    type="tel"
                    id="contactPhone"
                    value={formData.general.contactPhone}
                    onChange={(e) => handleInputChange("general", "contactPhone", e.target.value)}
                    placeholder="Enter contact phone"
                    className="form-control"
                  />
                </div>

                <div className="form-group full-width">
                  <label htmlFor="address">
                    <FaMapMarkerAlt />
                    Address
                  </label>
                  <textarea
                    id="address"
                    value={formData.general.address}
                    onChange={(e) => handleInputChange("general", "address", e.target.value)}
                    placeholder="Enter business address"
                    className="form-control"
                    rows="3"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="supportLink">
                    <FaExternalLinkAlt />
                    Support Link
                  </label>
                  <input
                    type="url"
                    id="supportLink"
                    value={formData.general.supportLink}
                    onChange={(e) => handleInputChange("general", "supportLink", e.target.value)}
                    placeholder="Enter support/help center URL"
                    className="form-control"
                  />
                </div>

                {/* Social Media Links Section */}
                <div className="social-media-section">
                  <div className="form-section-title">
                    <h3>
                      <FaGlobe />
                      Social Media Links
                    </h3>
                    <p>Configure your social media presence</p>
                  </div>

                  <div className="form-grid">
                    <div className="form-group">
                      <label htmlFor="facebookLink">
                        <FaFacebookF />
                        Facebook URL
                      </label>
                      <div className="social-input-group">
                        <div className="social-icon-wrapper facebook">
                          <FaFacebookF />
                        </div>
                        <input
                          type="url"
                          id="facebookLink"
                          value={formData.general.socialLinks.facebook}
                          onChange={(e) => handleSocialLinkChange("facebook", e.target.value)}
                          placeholder="https://facebook.com/yourpage"
                          className="form-control"
                        />
                      </div>
                    </div>

                    <div className="form-group">
                      <label htmlFor="twitterLink">
                        <FaTwitter />
                        Twitter URL
                      </label>
                      <div className="social-input-group">
                        <div className="social-icon-wrapper twitter">
                          <FaTwitter />
                        </div>
                        <input
                          type="url"
                          id="twitterLink"
                          value={formData.general.socialLinks.twitter}
                          onChange={(e) => handleSocialLinkChange("twitter", e.target.value)}
                          placeholder="https://twitter.com/yourhandle"
                          className="form-control"
                        />
                      </div>
                    </div>

                    <div className="form-group">
                      <label htmlFor="instagramLink">
                        <FaInstagram />
                        Instagram URL
                      </label>
                      <div className="social-input-group">
                        <div className="social-icon-wrapper instagram">
                          <FaInstagram />
                        </div>
                        <input
                          type="url"
                          id="instagramLink"
                          value={formData.general.socialLinks.instagram}
                          onChange={(e) => handleSocialLinkChange("instagram", e.target.value)}
                          placeholder="https://instagram.com/yourhandle"
                          className="form-control"
                        />
                      </div>
                    </div>
                  </div>
                </div>


              </div>
            </div>

            {/* Financial Settings Section */}
            <div className="settings-section">
              <div className="section-header">
                <h2>
                  <FaDollarSign className="section-icon" />
                  Financial Settings
                </h2>
                <p>Platform commission and financial configuration</p>
              </div>

              <div className="form-grid">
                <div className="form-group">
                  <label htmlFor="platformCommissionPercentage">
                    <FaPercentage />
                    Platform Commission (%)
                  </label>
                  <input
                    type="number"
                    id="platformCommissionPercentage"
                    value={formData.financial.platformCommissionPercentage}
                    onChange={(e) => handleInputChange("financial", "platformCommissionPercentage", parseFloat(e.target.value) || 0)}
                    placeholder="Enter commission percentage"
                    className="form-control"
                    min="0"
                    max="100"
                    step="0.1"
                  />
                  <small className="form-text">
                    Percentage of each sale that goes to the platform (0-100%)
                  </small>
                </div>
              </div>
            </div>
          </form>
        )}

        {errors.settings && (
          <div className="AdminSettings__error">
            <p>Error: {errors.settings}</p>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminSettings;
