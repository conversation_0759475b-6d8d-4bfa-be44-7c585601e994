/**
 * Test script for URL parsing in chunk assembler
 */

// Test URL parsing logic
function testUrlParsing() {
  console.log('=== Testing URL Parsing ===\n');
  
  // Sample URLs that might be generated by S3
  const testUrls = [
    'https://xosports.s3.amazonaws.com/uploads/chunk/1752749473782-blob',
    'https://xosports.s3.us-east-1.amazonaws.com/uploads/chunk/1752749473782-blob',
    'https://s3.amazonaws.com/xosports/uploads/chunk/1752749473782-blob',
    'https://xosports.s3.amazonaws.com/uploads/chunk/1752746164336-blob'
  ];
  
  testUrls.forEach((testUrl, index) => {
    console.log(`Test ${index + 1}: ${testUrl}`);
    
    try {
      const url = new URL(testUrl);
      const chunkKey = url.pathname.substring(1); // Remove leading slash
      
      console.log(`  Hostname: ${url.hostname}`);
      console.log(`  Pathname: ${url.pathname}`);
      console.log(`  Extracted key: ${chunkKey}`);
      console.log(`  ✅ Parsing successful\n`);
    } catch (error) {
      console.log(`  ❌ Parsing failed: ${error.message}\n`);
    }
  });
}

// Test the actual logic from chunk assembler
function testChunkKeyExtraction(chunkUrl) {
  console.log(`Testing chunk key extraction for: ${chunkUrl}`);
  
  if (chunkUrl.startsWith('https://') || chunkUrl.includes('amazonaws.com')) {
    const url = new URL(chunkUrl);
    const chunkKey = url.pathname.substring(1);
    console.log(`Extracted key: ${chunkKey}`);
    return chunkKey;
  } else {
    console.log('Not an S3 URL');
    return null;
  }
}

// Run tests
if (require.main === module) {
  testUrlParsing();
  
  console.log('=== Testing Specific URLs ===\n');
  
  // Test with the actual URLs from the logs
  const actualUrls = [
    'https://xosports.s3.amazonaws.com/uploads/chunk/1752749473782-blob',
    'https://xosports.s3.amazonaws.com/uploads/chunk/1752746164336-blob'
  ];
  
  actualUrls.forEach(url => {
    testChunkKeyExtraction(url);
    console.log('');
  });
}

module.exports = {
  testUrlParsing,
  testChunkKeyExtraction
};
