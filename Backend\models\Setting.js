const mongoose = require('mongoose');

const settingSchema = new mongoose.Schema({
  general: {
    siteName: { type: String, default: "XOSportsHub" },
    siteLogo: { type: String }, // Cloudinary URL or local path
    siteFavicon: { type: String }, // Cloudinary URL or local path for favicon
    contactEmail: { type: String },
    contactPhone: { type: String },
    address: { type: String },
    supportLink: { type: String }, // For help center or live chat
    socialLinks: {
      facebook: { type: String, trim: true },
      twitter: { type: String, trim: true },
      instagram: { type: String, trim: true }
    }
  },
  financial: {
    platformCommissionPercentage: { type: Number, default: 5 }, // Fixed typo: was platformcommisionin%
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  updatedBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  }
});

// Update the updatedAt field before saving
settingSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Ensure only one settings document exists (singleton pattern)
settingSchema.statics.getSingleton = async function() {
  let settings = await this.findOne();
  if (!settings) {
    settings = await this.create({});
  }
  return settings;
};

module.exports = mongoose.model("Setting", settingSchema);
